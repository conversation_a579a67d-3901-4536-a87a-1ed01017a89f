
CREATE TABLE IF NOT EXISTS admin_keys (
    id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
    access_key TEXT NOT NULL UNIQUE,
    hwid TEXT,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    assigned_at TIMESTAMP WITH TIME ZONE,
    name TEXT DEFAULT 'Administrator'
);

CREATE TABLE IF NOT EXISTS script_requests (
    id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    game_name TEXT NOT NULL,
    game_link TEXT NOT NULL,
    script_description TEXT NOT NULL,
    discord_username TEXT,
    status TEXT DEFAULT 'Pending'
);

CREATE TABLE IF NOT EXISTS users (
    id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
    hwid TEXT NOT NULL UNIQUE,
    status TEXT DEFAULT 'neutral',
    reason TEXT,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

CREATE TABLE IF NOT EXISTS blacklisted_hwids (
    id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
    hwid TEXT NOT NULL UNIQUE,
    reason TEXT,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);
CREATE TABLE IF NOT EXISTS page_visits (
    id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
    hwid TEXT,
    path TEXT,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

CREATE TABLE IF NOT EXISTS scripts (
    id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
    name TEXT NOT NULL,
    description TEXT,
    game_name TEXT,
    script_content TEXT,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

CREATE TABLE IF NOT EXISTS script_ratings (
    id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
    script_id UUID REFERENCES scripts(id),
    hwid TEXT NOT NULL,
    rating INTEGER CHECK (rating >= 1 AND rating <= 5),
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    UNIQUE(script_id, hwid)
);


ALTER TABLE admin_keys ENABLE ROW LEVEL SECURITY;
ALTER TABLE script_requests ENABLE ROW LEVEL SECURITY;
ALTER TABLE users ENABLE ROW LEVEL SECURITY;
ALTER TABLE blacklisted_hwids ENABLE ROW LEVEL SECURITY;
ALTER TABLE page_visits ENABLE ROW LEVEL SECURITY;
ALTER TABLE scripts ENABLE ROW LEVEL SECURITY;
ALTER TABLE script_ratings ENABLE ROW LEVEL SECURITY;


DROP POLICY IF EXISTS "Block all access to admin_keys" ON admin_keys;

DROP POLICY IF EXISTS "Allow public script submissions" ON script_requests;
DROP POLICY IF EXISTS "Allow authenticated script submissions" ON script_requests;
DROP POLICY IF EXISTS "Block public reading of script_requests" ON script_requests;
DROP POLICY IF EXISTS "Block public updates to script_requests" ON script_requests;
DROP POLICY IF EXISTS "Block public deletes from script_requests" ON script_requests;

DROP POLICY IF EXISTS "Block all access to users" ON users;
DROP POLICY IF EXISTS "Allow public user reads" ON users;
DROP POLICY IF EXISTS "Allow public user creation" ON users;
DROP POLICY IF EXISTS "Block public user updates" ON users;
DROP POLICY IF EXISTS "Block public user deletes" ON users;

DROP POLICY IF EXISTS "Block all access to blacklisted_hwids" ON blacklisted_hwids;

DROP POLICY IF EXISTS "Allow public page visits" ON page_visits;
DROP POLICY IF EXISTS "Block reading page visits" ON page_visits;

DROP POLICY IF EXISTS "Allow public script reading" ON scripts;
DROP POLICY IF EXISTS "Block public script modifications" ON scripts;

DROP POLICY IF EXISTS "Allow public script ratings read" ON script_ratings;
DROP POLICY IF EXISTS "Allow public script ratings insert" ON script_ratings;
DROP POLICY IF EXISTS "Block script ratings modifications" ON script_ratings;
DROP POLICY IF EXISTS "Block script ratings deletions" ON script_ratings;

CREATE POLICY "Block all access to admin_keys" ON admin_keys
    FOR ALL USING (false);

CREATE POLICY "Allow anon script submissions" ON script_requests
    FOR INSERT 
    TO anon
    WITH CHECK (true);

CREATE POLICY "Allow authenticated script submissions" ON script_requests
    FOR INSERT 
    TO authenticated
    WITH CHECK (true);

CREATE POLICY "Block public reading of script_requests" ON script_requests
    FOR SELECT USING (false);

CREATE POLICY "Block public updates to script_requests" ON script_requests
    FOR UPDATE USING (false);

CREATE POLICY "Block public deletes from script_requests" ON script_requests
    FOR DELETE USING (false);


CREATE POLICY "Allow public user reads" ON users
    FOR SELECT
    USING (true);

CREATE POLICY "Allow public user creation" ON users
    FOR INSERT
    WITH CHECK (true);

CREATE POLICY "Block public user updates" ON users
    FOR UPDATE
    USING (false);

CREATE POLICY "Block public user deletes" ON users
    FOR DELETE
    USING (false);

CREATE POLICY "Block all access to blacklisted_hwids" ON blacklisted_hwids
    FOR ALL USING (false);

CREATE POLICY "Allow public page visits" ON page_visits
    FOR INSERT WITH CHECK (true);

CREATE POLICY "Block reading page visits" ON page_visits
    FOR SELECT USING (false);

CREATE POLICY "Allow public script reading" ON scripts
    FOR SELECT USING (true);

CREATE POLICY "Block public script modifications" ON scripts
    FOR ALL USING (false);

CREATE POLICY "Allow public script ratings read" ON script_ratings
    FOR SELECT USING (true);

CREATE POLICY "Allow public script ratings insert" ON script_ratings
    FOR INSERT WITH CHECK (true);

CREATE POLICY "Block script ratings modifications" ON script_ratings
    FOR UPDATE USING (false);

CREATE POLICY "Block script ratings deletions" ON script_ratings
    FOR DELETE USING (false);


SELECT schemaname, tablename, rowsecurity 
FROM pg_tables 
WHERE tablename IN (
    'admin_keys', 
    'script_requests', 
    'users', 
    'blacklisted_hwids',
    'page_visits',
    'scripts',
    'script_ratings'
)
ORDER BY tablename;

SELECT schemaname, tablename, policyname, permissive, roles, cmd
FROM pg_policies 
WHERE tablename IN (
    'admin_keys', 
    'script_requests', 
    'users', 
    'blacklisted_hwids',
    'page_visits',
    'scripts',
    'script_ratings'
)

SELECT schemaname, tablename, policyname, permissive, roles, cmd
FROM pg_policies 
WHERE tablename = 'users'
ORDER BY policyname;

ORDER BY tablename, policyname;
[Error] Failed to load resource: the server responded with a status of 401 () (users, line 0)
[Error] Error ensuring user exists: – {code: "42501", details: null, hint: null, …}
{code: "42501", details: null, hint: null, message: "new row violates row-level security policy for table \"users\""}Object
	(anonymous function) (bloxsync.com:77)
	(anonymous function) (index-a9efe3e7.js:131:83793)
[Error] Failed to load resource: the server responded with a status of 406 () (users, line 0)
[Error] User check error: – {code: "PGRST116", details: "The result contains 0 rows", hint: null, …}
{code: "PGRST116", details: "The result contains 0 rows", hint: null, message: "JSON object requested, multiple (or no) rows returned"}Object
	(anonymous function) (bloxsync.com:77)
	(anonymous function) (index-a9efe3e7.js:131:83929)
[Error] Failed to load resource: the server responded with a status of 401 () (users, line 0)
[Error] Error ensuring user exists: – {code: "42501", details: null, hint: null, …}
{code: "42501", details: null, hint: null, message: "new row violates row-level security policy for table \"users\""}Object
	(anonymous function) (bloxsync.com:77)
	(anonymous function) (index-a9efe3e7.js:131:83793)
[Error] Failed to load resource: the server responded with a status of 406 () (users, line 0)
[Error] User check error: – {code: "PGRST116", details: "The result contains 0 rows", hint: null, …}
{code: "PGRST116", details: "The result contains 0 rows", hint: null, message: "JSON object requested, multiple (or no) rows returned"}Object
	(anonymous function) (bloxsync.com:77)
	(anonymous function) (index-a9efe3e7.js:131:83929)
[Error] Failed to load resource: the server responded with a status of 401 () (script_requests, line 0)