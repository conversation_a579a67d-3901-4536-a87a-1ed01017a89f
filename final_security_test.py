#!/usr/bin/env python3
"""
Final Security Test - Run this AFTER your friend applies all fixes
This will verify that the security vulnerabilities are completely fixed
"""

import requests
import json
from datetime import datetime

def test_final_security():
    """Comprehensive security test"""
    
    supabase_url = "https://ywecnsbsgpeqzyxxxhhm.supabase.co"
    anon_key = "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6Inl3ZWNuc2JzZ3BlcXp5eHh4aGhtIiwicm9sZSI6ImFub24iLCJpYXQiOjE3NTA4ODYzNDAsImV4cCI6MjA2NjQ2MjM0MH0.nJ8X9nos1IMvO3zsqrSP9vIuAsAVLUt48kIrtUOfocI"
    
    headers = {
        'Authorization': f'Bearer {anon_key}',
        'apikey': anon_key,
        'Content-Type': 'application/json'
    }
    
    print("🔒 FINAL SECURITY TEST")
    print("=" * 50)
    print(f"Testing at: {datetime.now()}")
    print()
    
    tests_passed = 0
    tests_failed = 0
    
    # Test 1: Admin keys should be completely blocked
    print("1. 🔑 Testing admin_keys protection...")
    try:
        response = requests.get(f"{supabase_url}/rest/v1/admin_keys", headers=headers, timeout=10)
        if response.status_code == 200:
            data = response.json()
            if len(data) == 0:
                print("   ✅ SECURE: Admin keys table returns empty (RLS working)")
                tests_passed += 1
            else:
                print(f"   ❌ VULNERABLE: Found {len(data)} admin keys exposed!")
                tests_failed += 1
        elif response.status_code in [401, 403]:
            print("   ✅ SECURE: Admin keys access properly blocked")
            tests_passed += 1
        else:
            print(f"   ⚠️ UNEXPECTED: Status {response.status_code}")
            tests_failed += 1
    except Exception as e:
        print(f"   ⚠️ ERROR: {e}")
        tests_failed += 1
    
    # Test 2: Users table should be blocked
    print("\n2. 👥 Testing users table protection...")
    try:
        response = requests.get(f"{supabase_url}/rest/v1/users", headers=headers, timeout=10)
        if response.status_code == 200:
            data = response.json()
            if len(data) == 0:
                print("   ✅ SECURE: Users table returns empty (RLS working)")
                tests_passed += 1
            else:
                print(f"   ❌ VULNERABLE: Found {len(data)} users exposed!")
                tests_failed += 1
        elif response.status_code in [401, 403]:
            print("   ✅ SECURE: Users table access properly blocked")
            tests_passed += 1
        else:
            print(f"   ⚠️ UNEXPECTED: Status {response.status_code}")
            tests_failed += 1
    except Exception as e:
        print(f"   ⚠️ ERROR: {e}")
        tests_failed += 1
    
    # Test 3: Script requests should be blocked for reading
    print("\n3. 📝 Testing script_requests read protection...")
    try:
        response = requests.get(f"{supabase_url}/rest/v1/script_requests", headers=headers, timeout=10)
        if response.status_code == 200:
            data = response.json()
            if len(data) == 0:
                print("   ✅ SECURE: Script requests returns empty (RLS working)")
                tests_passed += 1
            else:
                print(f"   ❌ VULNERABLE: Can still read {len(data)} script requests!")
                tests_failed += 1
        elif response.status_code in [401, 403]:
            print("   ✅ SECURE: Script requests read access properly blocked")
            tests_passed += 1
        else:
            print(f"   ⚠️ UNEXPECTED: Status {response.status_code}")
            tests_failed += 1
    except Exception as e:
        print(f"   ⚠️ ERROR: {e}")
        tests_failed += 1
    
    # Test 4: Script submissions should still work
    print("\n4. ✍️ Testing script submission functionality...")
    test_payload = {
        "game_name": f"Security Test {datetime.now().strftime('%H:%M:%S')}",
        "game_link": "https://www.roblox.com/games/123456789/Security-Test",
        "script_description": "Final security test - verifying submissions still work",
        "discord_username": "security_tester_final",
        "status": "Pending"
    }
    
    try:
        response = requests.post(
            f"{supabase_url}/rest/v1/script_requests",
            json=test_payload,
            headers=headers,
            timeout=10
        )
        if response.status_code in [200, 201]:
            print("   ✅ WORKING: Script submissions still function correctly")
            tests_passed += 1
        else:
            print(f"   ❌ BROKEN: Script submissions failed (Status: {response.status_code})")
            print(f"      Response: {response.text[:100]}...")
            tests_failed += 1
    except Exception as e:
        print(f"   ❌ ERROR: Script submission failed - {e}")
        tests_failed += 1
    
    # Test 5: Blacklisted HWIDs should be blocked
    print("\n5. 🚫 Testing blacklisted_hwids protection...")
    try:
        response = requests.get(f"{supabase_url}/rest/v1/blacklisted_hwids", headers=headers, timeout=10)
        if response.status_code == 200:
            data = response.json()
            if len(data) == 0:
                print("   ✅ SECURE: Blacklisted HWIDs returns empty (RLS working)")
                tests_passed += 1
            else:
                print(f"   ❌ VULNERABLE: Found {len(data)} blacklisted HWIDs exposed!")
                tests_failed += 1
        elif response.status_code in [401, 403]:
            print("   ✅ SECURE: Blacklisted HWIDs access properly blocked")
            tests_passed += 1
        else:
            print(f"   ⚠️ UNEXPECTED: Status {response.status_code}")
            tests_failed += 1
    except Exception as e:
        print(f"   ⚠️ ERROR: {e}")
        tests_failed += 1
    
    # Final Results
    print("\n" + "=" * 50)
    print("🎯 FINAL SECURITY TEST RESULTS")
    print("=" * 50)
    
    total_tests = tests_passed + tests_failed
    success_rate = (tests_passed / total_tests * 100) if total_tests > 0 else 0
    
    print(f"Tests Passed: {tests_passed}")
    print(f"Tests Failed: {tests_failed}")
    print(f"Success Rate: {success_rate:.1f}%")
    
    if tests_failed == 0:
        print("\n🎉 CONGRATULATIONS! 🎉")
        print("✅ ALL SECURITY VULNERABILITIES FIXED!")
        print("✅ Database is now completely secure")
        print("✅ App functionality preserved")
        print("✅ Your friend's app is safe to use")
        print("\n💬 Tell your friend: 'Security fixes successful! Your app is now secure!'")
    elif tests_failed <= 2:
        print("\n⚠️ MOSTLY SECURE")
        print("Most vulnerabilities are fixed, but some issues remain.")
        print("Review the failed tests above and apply additional fixes.")
    else:
        print("\n❌ STILL VULNERABLE")
        print("Critical security issues remain!")
        print("Your friend needs to apply the SQL fixes in Supabase dashboard.")
        print("Run the commands in URGENT_SUPABASE_FIXES.sql")
    
    print(f"\nTest completed at: {datetime.now()}")
    return tests_failed == 0

if __name__ == "__main__":
    success = test_final_security()
    exit(0 if success else 1)
