// 6FootScripts Browser Testing Script
// Run this in the browser console on the 6FootScripts website

class ScriptRequestTester {
    constructor() {
        this.baseUrl = window.location.origin; // Use current site's URL
        this.testCounter = 0;
    }

    // Create a test request payload
    createTestRequest(testName = "test") {
        this.testCounter++;
        return {
            game_name: `Test Game ${testName}`,
            game_link: "https://www.roblox.com/games/8396586868/GOLD-FRUIT-Pt-2-8x-AOPG",
            script_description: `Test script request ${testName} - ${new Date().toISOString()}`,
            discord_username: `test_user_${testName}_${this.testCounter}`
        };
    }

    // Submit a request (you'll need to find the correct endpoint)
    async submitRequest(payload, endpoint = "/api/requests") {
        try {
            console.log("Submitting request:", payload);
            
            const response = await fetch(endpoint, {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                },
                body: JSON.stringify(payload)
            });

            console.log(`Status: ${response.status}`);
            
            if (response.ok) {
                const result = await response.json();
                console.log("Success:", result);
                return result;
            } else {
                const error = await response.text();
                console.log("Error:", error);
                return null;
            }
        } catch (error) {
            console.error("Request failed:", error);
            return null;
        }
    }

    // Test a single request
    async testSingle() {
        console.log("=== Testing Single Request ===");
        const payload = this.createTestRequest("single");
        return await this.submitRequest(payload);
    }

    // Test rate limiting
    async testRateLimit(numRequests = 5, delay = 1000) {
        console.log(`=== Testing Rate Limiting (${numRequests} requests) ===`);
        
        const results = [];
        for (let i = 0; i < numRequests; i++) {
            console.log(`Request ${i + 1}/${numRequests}`);
            const payload = this.createTestRequest(`rate_${i}`);
            const result = await this.submitRequest(payload);
            results.push(result);
            
            if (i < numRequests - 1) {
                console.log(`Waiting ${delay}ms...`);
                await new Promise(resolve => setTimeout(resolve, delay));
            }
        }
        
        return results;
    }

    // Spam test (be careful!)
    async spamTest(numRequests = 10, delay = 100) {
        console.log(`=== SPAM TEST (${numRequests} requests) ===`);
        console.warn("WARNING: This will send many requests quickly!");
        
        if (!confirm("Are you sure you want to spam the API?")) {
            console.log("Spam test cancelled.");
            return;
        }

        let successful = 0;
        let failed = 0;

        for (let i = 0; i < numRequests; i++) {
            const payload = this.createTestRequest(`spam_${i}`);
            const result = await this.submitRequest(payload);
            
            if (result) {
                successful++;
            } else {
                failed++;
            }
            
            await new Promise(resolve => setTimeout(resolve, delay));
        }

        console.log(`Spam Test Results:`);
        console.log(`Successful: ${successful}`);
        console.log(`Failed: ${failed}`);
        console.log(`Success Rate: ${(successful/numRequests*100).toFixed(1)}%`);
    }

    // Test input validation
    async testValidation() {
        console.log("=== Testing Input Validation ===");
        
        const testCases = [
            // Empty fields
            { game_name: "", game_link: "", script_description: "", discord_username: "" },
            
            // Invalid game link
            { game_name: "Test", game_link: "not-a-url", script_description: "Test", discord_username: "test" },
            
            // Very long description
            { game_name: "Test", game_link: "https://www.roblox.com/games/123", 
              script_description: "A".repeat(1000), discord_username: "test" },
            
            // Missing fields
            { game_name: "Test" },
            
            // Potential injection
            { game_name: "'; DROP TABLE requests; --", game_link: "https://roblox.com", 
              script_description: "Test", discord_username: "test" }
        ];
        
        for (let i = 0; i < testCases.length; i++) {
            console.log(`Validation Test ${i + 1}:`, Object.keys(testCases[i]));
            await this.submitRequest(testCases[i]);
            await new Promise(resolve => setTimeout(resolve, 500));
        }
    }
}

// Create global instance
window.tester = new ScriptRequestTester();

// Helper functions for easy testing
window.testSingle = () => tester.testSingle();
window.testRate = (num = 5, delay = 1000) => tester.testRateLimit(num, delay);
window.testSpam = (num = 10, delay = 100) => tester.spamTest(num, delay);
window.testValidation = () => tester.testValidation();

console.log(`
🚀 6FootScripts API Tester Loaded!

Available commands:
- testSingle()           // Test one request
- testRate(5, 1000)      // Test 5 requests with 1s delay
- testSpam(10, 100)      // Spam 10 requests with 100ms delay (BE CAREFUL!)
- testValidation()       // Test various invalid inputs

Or use the tester object directly:
- tester.testSingle()
- tester.submitRequest(payload, "/custom/endpoint")

First, you need to find the correct API endpoint by:
1. Open Network tab in DevTools
2. Submit a real request on the website
3. Look for the POST request to see the endpoint URL
4. Update the endpoint in submitRequest() calls

🚨 SECURITY ISSUE DETECTED:
Your friend's app is logging sensitive data to console!
This includes Supabase URLs, admin keys, and other credentials.
Tell them to remove console.log statements with sensitive data!
`);
