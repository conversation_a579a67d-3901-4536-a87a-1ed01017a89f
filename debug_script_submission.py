#!/usr/bin/env python3
"""
Debug Script Submission - Let's figure out what's actually wrong
"""

import requests
import json

def debug_submission():
    supabase_url = "https://ywecnsbsgpeqzyxxxhhm.supabase.co"
    anon_key = "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6Inl3ZWNuc2JzZ3BlcXp5eHh4aGhtIiwicm9sZSI6ImFub24iLCJpYXQiOjE3NTA4ODYzNDAsImV4cCI6MjA2NjQ2MjM0MH0.nJ8X9nos1IMvO3zsqrSP9vIuAsAVLUt48kIrtUOfocI"
    
    headers = {
        'Authorization': f'Bearer {anon_key}',
        'apikey': anon_key,
        'Content-Type': 'application/json'
    }
    
    print("🔍 DEBUGGING SCRIPT SUBMISSION")
    print("=" * 50)
    
    # Test 1: Check what role we're actually using
    print("\n1. Testing current user role...")
    try:
        response = requests.get(f"{supabase_url}/auth/v1/user", headers=headers)
        print(f"Auth status: {response.status_code}")
        if response.status_code == 200:
            user_data = response.json()
            print(f"User role: {user_data.get('role', 'unknown')}")
        else:
            print("Not authenticated - using anon role")
    except Exception as e:
        print(f"Auth check failed: {e}")
    
    # Test 2: Check table structure
    print("\n2. Checking script_requests table structure...")
    try:
        # Try to get table info (this might work even with RLS)
        response = requests.get(f"{supabase_url}/rest/v1/script_requests?limit=0", headers=headers)
        print(f"Table access status: {response.status_code}")
        print(f"Response headers: {dict(response.headers)}")
    except Exception as e:
        print(f"Table check failed: {e}")
    
    # Test 3: Try minimal payload
    print("\n3. Testing minimal payload...")
    minimal_payload = {
        "game_name": "Test",
        "game_link": "https://test.com",
        "script_description": "Test",
        "discord_username": "test"
    }
    
    try:
        response = requests.post(
            f"{supabase_url}/rest/v1/script_requests",
            json=minimal_payload,
            headers=headers
        )
        print(f"Minimal payload status: {response.status_code}")
        print(f"Response: {response.text}")
    except Exception as e:
        print(f"Minimal payload failed: {e}")
    
    # Test 4: Try with different headers
    print("\n4. Testing with different headers...")
    simple_headers = {
        'apikey': anon_key,
        'Content-Type': 'application/json'
    }
    
    try:
        response = requests.post(
            f"{supabase_url}/rest/v1/script_requests",
            json=minimal_payload,
            headers=simple_headers
        )
        print(f"Simple headers status: {response.status_code}")
        print(f"Response: {response.text}")
    except Exception as e:
        print(f"Simple headers failed: {e}")
    
    # Test 5: Check if it's a field issue
    print("\n5. Testing individual fields...")
    field_tests = [
        {"game_name": "Test"},
        {"game_name": "Test", "game_link": "https://test.com"},
        {"game_name": "Test", "game_link": "https://test.com", "script_description": "Test"},
    ]
    
    for i, payload in enumerate(field_tests):
        try:
            response = requests.post(
                f"{supabase_url}/rest/v1/script_requests",
                json=payload,
                headers=headers
            )
            print(f"Field test {i+1} status: {response.status_code}")
            if response.status_code != 201:
                print(f"  Response: {response.text}")
        except Exception as e:
            print(f"Field test {i+1} failed: {e}")

if __name__ == "__main__":
    debug_submission()
