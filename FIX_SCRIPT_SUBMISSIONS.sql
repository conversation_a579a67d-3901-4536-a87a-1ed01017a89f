-- 🔧 FIX SCRIPT SUBMISSIONS
-- The RLS is blocking script submissions - this fixes it

-- First, drop the existing broken policy
DROP POLICY "Allow public script submissions" ON script_requests;

-- Create a proper policy that allows public submissions
CREATE POLICY "Allow public script submissions" ON script_requests
    FOR INSERT
    TO anon
    WITH CHECK (true);

-- Also allow submissions from authenticated users (just in case)
CREATE POLICY "Allow authenticated script submissions" ON script_requests
    FOR INSERT
    TO authenticated
    WITH CHECK (true);

-- Verify the policies exist
SELECT schemaname, tablename, policyname, permissive, roles, cmd, qual, with_check
FROM pg_policies
WHERE tablename = 'script_requests' AND policyname LIKE '%script submissions%';
