-- 🔧 FIX SCRIPT SUBMISSIONS
-- The RLS is blocking script submissions - this fixes it

-- Drop the overly restrictive policy
DROP POLICY IF EXISTS "Allow public script submissions" ON script_requests;

-- Create a proper policy that allows public submissions
CREATE POLICY "Allow public script submissions" ON script_requests
    FOR INSERT 
    TO anon
    WITH CHECK (true);

-- Verify the policy exists
SELECT schemaname, tablename, policyname, permissive, roles, cmd, qual, with_check
FROM pg_policies 
WHERE tablename = 'script_requests' AND policyname = 'Allow public script submissions';
