// Console <PERSON>anner - Find all sensitive data being logged
// Run this in browser console to detect security leaks

class ConsoleScannerTool {
    constructor() {
        this.sensitivePatterns = [
            // Supabase URLs
            /https:\/\/[a-z0-9]+\.supabase\.co/gi,
            
            // JWT tokens
            /eyJ[A-Za-z0-9-_=]+\.[A-Za-z0-9-_=]+\.?[A-Za-z0-9-_.+/=]*/g,
            
            // API keys (common patterns)
            /[a-zA-Z0-9]{20,}/g,
            
            // Admin/password related
            /admin|password|secret|key|token/gi,
            
            // UUIDs
            /[0-9a-f]{8}-[0-9a-f]{4}-[0-9a-f]{4}-[0-9a-f]{4}-[0-9a-f]{12}/gi,
            
            // Email addresses
            /[a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\.[a-zA-Z]{2,}/g
        ];
        
        this.foundIssues = [];
        this.originalConsole = {};
        this.isMonitoring = false;
    }
    
    // Override console methods to monitor what's being logged
    startMonitoring() {
        if (this.isMonitoring) {
            console.warn("Already monitoring console!");
            return;
        }
        
        this.isMonitoring = true;
        
        // Store original console methods
        this.originalConsole.log = console.log;
        this.originalConsole.warn = console.warn;
        this.originalConsole.error = console.error;
        this.originalConsole.info = console.info;
        
        const scanner = this;
        
        // Override console.log
        console.log = function(...args) {
            scanner.scanArguments('log', args);
            scanner.originalConsole.log.apply(console, args);
        };
        
        // Override console.warn
        console.warn = function(...args) {
            scanner.scanArguments('warn', args);
            scanner.originalConsole.warn.apply(console, args);
        };
        
        // Override console.error
        console.error = function(...args) {
            scanner.scanArguments('error', args);
            scanner.originalConsole.error.apply(console, args);
        };
        
        // Override console.info
        console.info = function(...args) {
            scanner.scanArguments('info', args);
            scanner.originalConsole.info.apply(console, args);
        };
        
        console.log("🔍 Console Scanner activated! Monitoring for sensitive data...");
    }
    
    stopMonitoring() {
        if (!this.isMonitoring) {
            console.warn("Not currently monitoring!");
            return;
        }
        
        // Restore original console methods
        console.log = this.originalConsole.log;
        console.warn = this.originalConsole.warn;
        console.error = this.originalConsole.error;
        console.info = this.originalConsole.info;
        
        this.isMonitoring = false;
        console.log("🔍 Console Scanner deactivated!");
    }
    
    scanArguments(method, args) {
        const timestamp = new Date().toISOString();
        
        args.forEach((arg, index) => {
            const argString = this.convertToString(arg);
            
            this.sensitivePatterns.forEach(pattern => {
                const matches = argString.match(pattern);
                if (matches) {
                    matches.forEach(match => {
                        this.foundIssues.push({
                            timestamp,
                            method,
                            argIndex: index,
                            pattern: pattern.toString(),
                            match: match,
                            fullArg: argString.substring(0, 200) + (argString.length > 200 ? '...' : ''),
                            severity: this.getSeverity(match)
                        });
                    });
                }
            });
        });
    }
    
    convertToString(arg) {
        if (typeof arg === 'string') {
            return arg;
        } else if (typeof arg === 'object') {
            try {
                return JSON.stringify(arg);
            } catch (e) {
                return arg.toString();
            }
        } else {
            return String(arg);
        }
    }
    
    getSeverity(match) {
        if (match.includes('supabase.co')) return 'HIGH';
        if (match.startsWith('eyJ')) return 'CRITICAL'; // JWT token
        if (match.length > 25 && /[A-Za-z0-9]{25,}/.test(match)) return 'HIGH'; // Long API key
        if (/admin|password|secret/i.test(match)) return 'HIGH';
        return 'MEDIUM';
    }
    
    generateReport() {
        console.log("\n🚨 CONSOLE SECURITY SCAN REPORT 🚨");
        console.log("=" * 50);
        
        if (this.foundIssues.length === 0) {
            console.log("✅ No sensitive data detected in console logs!");
            return;
        }
        
        // Group by severity
        const bySeverity = {
            CRITICAL: [],
            HIGH: [],
            MEDIUM: []
        };
        
        this.foundIssues.forEach(issue => {
            bySeverity[issue.severity].push(issue);
        });
        
        // Report critical issues first
        Object.keys(bySeverity).forEach(severity => {
            const issues = bySeverity[severity];
            if (issues.length === 0) return;
            
            const emoji = severity === 'CRITICAL' ? '🔥' : severity === 'HIGH' ? '⚠️' : 'ℹ️';
            console.log(`\n${emoji} ${severity} SEVERITY (${issues.length} issues):`);
            
            issues.forEach((issue, index) => {
                console.log(`  ${index + 1}. ${issue.method.toUpperCase()}: ${issue.match}`);
                console.log(`     Time: ${issue.timestamp}`);
                console.log(`     Context: ${issue.fullArg}`);
                console.log("");
            });
        });
        
        // Summary
        console.log(`\n📊 SUMMARY:`);
        console.log(`Total Issues: ${this.foundIssues.length}`);
        console.log(`Critical: ${bySeverity.CRITICAL.length}`);
        console.log(`High: ${bySeverity.HIGH.length}`);
        console.log(`Medium: ${bySeverity.MEDIUM.length}`);
        
        // Recommendations
        console.log(`\n💡 RECOMMENDATIONS:`);
        if (bySeverity.CRITICAL.length > 0) {
            console.log("🔥 CRITICAL: Remove JWT tokens from console logs immediately!");
        }
        if (bySeverity.HIGH.length > 0) {
            console.log("⚠️ HIGH: Remove API keys and sensitive URLs from logs!");
        }
        console.log("📝 Replace sensitive console.log with generic messages");
        console.log("🔒 Use environment variables for sensitive data");
        console.log("🧹 Clean up debug logs before production");
    }
    
    // Quick scan of current console history (if available)
    scanCurrentConsole() {
        console.log("🔍 Scanning current console for sensitive data...");
        
        // This is a basic scan - in real apps you'd need to hook into the console earlier
        const consoleElement = document.querySelector('.console-log-level');
        if (consoleElement) {
            console.log("Found console element, scanning...");
            // Scan visible console content
        } else {
            console.log("⚠️ Cannot scan existing console history");
            console.log("💡 Start monitoring now and trigger the actions that log sensitive data");
        }
    }
}

// Create global scanner instance
window.consoleScanner = new ConsoleScannerTool();

// Helper functions
window.startScan = () => consoleScanner.startMonitoring();
window.stopScan = () => consoleScanner.stopMonitoring();
window.scanReport = () => consoleScanner.generateReport();
window.clearScanResults = () => {
    consoleScanner.foundIssues = [];
    console.log("🧹 Scan results cleared!");
};

console.log(`
🔍 CONSOLE SECURITY SCANNER LOADED!

Commands:
- startScan()     // Start monitoring console logs
- stopScan()      // Stop monitoring  
- scanReport()    // Generate security report
- clearScanResults() // Clear found issues

Usage:
1. Run startScan()
2. Use the app normally (login, submit requests, etc.)
3. Run scanReport() to see what sensitive data was logged
4. Run stopScan() when done

🚨 This will help find where your friend's app is logging:
- Supabase URLs
- JWT tokens  
- API keys
- Admin credentials
- User data
`);

// Auto-start monitoring
console.log("🚀 Auto-starting console monitoring...");
consoleScanner.startMonitoring();
