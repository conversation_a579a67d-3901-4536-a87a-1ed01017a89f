-- 🔧 FIX USERS TABLE ACCESS
-- The app needs to check/create users for HWID tracking

DROP POLICY "Block all access to users" ON users;

CREATE POLICY "Allow public user reads" ON users
    FOR SELECT 
    USING (true);

CREATE POLICY "Allow public user creation" ON users
    FOR INSERT 
    WITH CHECK (true);

CREATE POLICY "Block public user updates" ON users
    FOR UPDATE 
    USING (false);

CREATE POLICY "Block public user deletes" ON users
    FOR DELETE 
    USING (false);

SELECT schemaname, tablename, policyname, permissive, roles, cmd
FROM pg_policies 
WHERE tablename = 'users'
ORDER BY policyname;
