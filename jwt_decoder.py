#!/usr/bin/env python3
"""
JWT Token Decoder - Decode JWT tokens without verification
Just for analysis purposes!
"""

import json
import base64
from datetime import datetime

def decode_jwt_payload(token):
    """Decode JWT payload without verification"""
    try:
        # JWT format: header.payload.signature
        parts = token.split('.')
        
        if len(parts) != 3:
            print("Invalid JWT format - should have 3 parts separated by dots")
            return None
        
        header_encoded, payload_encoded, signature = parts
        
        # Decode header
        header_padded = header_encoded + '=' * (4 - len(header_encoded) % 4)
        header_bytes = base64.urlsafe_b64decode(header_padded)
        header = json.loads(header_bytes.decode('utf-8'))
        
        # Decode payload
        payload_padded = payload_encoded + '=' * (4 - len(payload_encoded) % 4)
        payload_bytes = base64.urlsafe_b64decode(payload_padded)
        payload = json.loads(payload_bytes.decode('utf-8'))
        
        return header, payload, signature
        
    except Exception as e:
        print(f"Error decoding JWT: {e}")
        return None

def analyze_supabase_token(header, payload):
    """Analyze Supabase-specific JWT claims"""
    print("🔍 SUPABASE TOKEN ANALYSIS")
    print("=" * 50)
    
    # Basic info
    print(f"Issuer: {payload.get('iss', 'Unknown')}")
    print(f"Project Reference: {payload.get('ref', 'Unknown')}")
    print(f"Role: {payload.get('role', 'Unknown')}")
    
    # Timestamps
    iat = payload.get('iat')
    exp = payload.get('exp')
    
    if iat:
        iat_date = datetime.fromtimestamp(iat)
        print(f"Issued At: {iat_date} ({iat})")
    
    if exp:
        exp_date = datetime.fromtimestamp(exp)
        print(f"Expires At: {exp_date} ({exp})")
        
        # Check if expired
        now = datetime.now()
        if exp_date > now:
            days_left = (exp_date - now).days
            print(f"Status: ✅ Valid (expires in {days_left} days)")
        else:
            print(f"Status: ❌ EXPIRED")
    
    # Role analysis
    role = payload.get('role', '').lower()
    if role == 'anon':
        print("🔓 Role Analysis: Anonymous/Public access")
        print("   - This is likely the public API key")
        print("   - Limited permissions")
        print("   - Safe to expose in frontend code")
    elif role == 'authenticated':
        print("🔐 Role Analysis: Authenticated user")
        print("   - User-level permissions")
        print("   - Should not be shared")
    elif role == 'service_role':
        print("🚨 Role Analysis: SERVICE ROLE - ADMIN ACCESS!")
        print("   - Full database access")
        print("   - Can bypass RLS policies")
        print("   - SHOULD NEVER BE EXPOSED!")
    else:
        print(f"❓ Role Analysis: Unknown role '{role}'")

def main():
    # The JWT token from your network request
    jwt_token = "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6Inl3ZWNuc2JzZ3BlcXp5eHh4aGhtIiwicm9sZSI6ImFub24iLCJpYXQiOjE3NTA4ODYzNDAsImV4cCI6MjA2NjQ2MjM0MH0.nJ8X9nos1IMvO3zsqrSP9vIuAsAVLUt48kIrtUOfocI"
    
    print("🔐 JWT TOKEN DECODER")
    print("=" * 50)
    print(f"Token: {jwt_token[:50]}...")
    print()
    
    result = decode_jwt_payload(jwt_token)
    
    if result:
        header, payload, signature = result
        
        print("📋 HEADER:")
        print(json.dumps(header, indent=2))
        print()
        
        print("📦 PAYLOAD:")
        print(json.dumps(payload, indent=2))
        print()
        
        print("✍️ SIGNATURE:")
        print(f"{signature[:20]}... (truncated)")
        print()
        
        # Supabase-specific analysis
        if payload.get('iss') == 'supabase':
            analyze_supabase_token(header, payload)
        
        print()
        print("🎯 WHAT THIS MEANS:")
        print("- This token identifies the Supabase project")
        print("- The 'ref' field shows the project ID")
        print("- The 'role' determines what permissions it has")
        print("- The request to /admin_keys suggests trying to get admin credentials")
        
    else:
        print("❌ Failed to decode JWT token")

if __name__ == "__main__":
    main()
