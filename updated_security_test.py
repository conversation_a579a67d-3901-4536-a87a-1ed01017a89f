#!/usr/bin/env python3
"""
Updated Security Test - Handles missing tables gracefully
Run this AFTER your friend applies the REBUILT fixes
"""

import requests
import json

def test_security_fixes():
    """Test if the security vulnerabilities are fixed"""
    
    # The same credentials that were exposed
    supabase_url = "https://ywecnsbsgpeqzyxxxhhm.supabase.co"
    anon_key = "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6Inl3ZWNuc2JzZ3BlcXp5eHh4aGhtIiwicm9sZSI6ImFub24iLCJpYXQiOjE3NTA4ODYzNDAsImV4cCI6MjA2NjQ2MjM0MH0.nJ8X9nos1IMvO3zsqrSP9vIuAsAVLUt48kIrtUOfocI"
    
    headers = {
        'Authorization': f'Bearer {anon_key}',
        'apikey': anon_key,
        'Content-Type': 'application/json'
    }
    
    print("🔒 UPDATED SECURITY TEST")
    print("=" * 40)
    
    # Test 1: Admin keys should be blocked
    print("\n1. Testing admin_keys access (should be BLOCKED):")
    try:
        response = requests.get(f"{supabase_url}/rest/v1/admin_keys", headers=headers)
        if response.status_code == 200:
            data = response.json()
            if len(data) == 0:
                print("✅ SECURE! Admin keys table returns empty (RLS working)")
            else:
                print(f"❌ STILL VULNERABLE! Found {len(data)} admin keys")
        elif response.status_code in [401, 403]:
            print("✅ SECURE! Admin keys access properly blocked")
        elif response.status_code == 404:
            print("ℹ️ INFO: Admin keys table doesn't exist yet (will be created)")
        else:
            print(f"⚠️ Unexpected response: {response.status_code}")
    except Exception as e:
        print(f"⚠️ Error testing admin_keys: {e}")
    
    # Test 2: Script requests should be limited
    print("\n2. Testing script_requests access:")
    try:
        response = requests.get(f"{supabase_url}/rest/v1/script_requests", headers=headers)
        if response.status_code == 200:
            data = response.json()
            if len(data) == 0:
                print("✅ SECURE! Script requests returns empty (RLS working)")
            else:
                print(f"❌ VULNERABLE! Can still see {len(data)} script requests")
        elif response.status_code in [401, 403]:
            print("✅ SECURE! Script requests read access properly blocked")
        elif response.status_code == 404:
            print("ℹ️ INFO: Script requests table doesn't exist yet (will be created)")
        else:
            print(f"⚠️ Unexpected response: {response.status_code}")
    except Exception as e:
        print(f"⚠️ Error testing script_requests: {e}")
    
    # Test 3: Users table should be blocked
    print("\n3. Testing users table access (should be BLOCKED):")
    try:
        response = requests.get(f"{supabase_url}/rest/v1/users", headers=headers)
        if response.status_code == 200:
            data = response.json()
            if len(data) == 0:
                print("✅ SECURE! Users table returns empty (RLS working)")
            else:
                print(f"❌ STILL VULNERABLE! Found {len(data)} users")
        elif response.status_code in [401, 403]:
            print("✅ SECURE! Users table access properly blocked")
        elif response.status_code == 404:
            print("ℹ️ INFO: Users table doesn't exist yet (will be created)")
        else:
            print(f"⚠️ Unexpected response: {response.status_code}")
    except Exception as e:
        print(f"⚠️ Error testing users: {e}")
    
    # Test 4: Script submission should still work
    print("\n4. Testing script submission (should still WORK):")
    test_payload = {
        "game_name": "Security Test Game",
        "game_link": "https://www.roblox.com/games/123456789/Test-Game",
        "script_description": "Testing if submissions still work after security fixes",
        "discord_username": "security_tester"
    }
    
    try:
        response = requests.post(
            f"{supabase_url}/rest/v1/script_requests",
            json=test_payload,
            headers=headers
        )
        if response.status_code in [200, 201]:
            print("✅ WORKING! Script submissions still work")
        elif response.status_code == 401:
            print("❌ BROKEN! Script submissions are blocked (this should work)")
        elif response.status_code == 404:
            print("ℹ️ INFO: Script requests table doesn't exist yet")
        else:
            print(f"⚠️ Unexpected response: {response.status_code}")
            print(f"   Response: {response.text}")
    except Exception as e:
        print(f"⚠️ Error testing submission: {e}")
    
    # Test 5: Blacklisted HWIDs should be blocked (if exists)
    print("\n5. Testing blacklisted_hwids access (optional table):")
    try:
        response = requests.get(f"{supabase_url}/rest/v1/blacklisted_hwids", headers=headers)
        if response.status_code == 200:
            data = response.json()
            if len(data) == 0:
                print("✅ SECURE! Blacklisted HWIDs returns empty (RLS working)")
            else:
                print(f"❌ VULNERABLE! Found {len(data)} blacklisted HWIDs")
        elif response.status_code in [401, 403]:
            print("✅ SECURE! Blacklisted HWIDs access properly blocked")
        elif response.status_code == 404:
            print("ℹ️ INFO: Blacklisted HWIDs table doesn't exist (that's okay)")
        else:
            print(f"⚠️ Unexpected response: {response.status_code}")
    except Exception as e:
        print(f"⚠️ Error testing blacklisted_hwids: {e}")
    
    print("\n" + "=" * 40)
    print("🎯 SECURITY TEST COMPLETE")
    print("\nNext steps:")
    print("1. If you see 'table doesn't exist' messages:")
    print("   → Run REBUILT_SUPABASE_FIXES.sql to create tables")
    print("2. If you see 'VULNERABLE' messages:")
    print("   → Run the SQL fixes to enable RLS")
    print("3. If you see 'SECURE' messages:")
    print("   → Great! That part is fixed")

if __name__ == "__main__":
    test_security_fixes()
