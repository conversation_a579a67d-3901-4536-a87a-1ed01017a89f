-- 🚨 URGENT SUPABASE SECURITY FIXES 🚨
-- Your friend needs to run these commands in Supabase SQL Editor IMMEDIATELY!

-- 1. ENABLE ROW LEVEL SECURITY ON ALL SENSITIVE TABLES
ALTER TABLE admin_keys ENABLE ROW LEVEL SECURITY;
ALTER TABLE script_requests ENABLE ROW LEVEL SECURITY;
ALTER TABLE users ENABLE ROW LEVEL SECURITY;
ALTER TABLE blacklisted_hwids ENABLE ROW LEVEL SECURITY;

-- 2. BLOCK ALL ACCESS TO ADMIN KEYS (only service role can access)
CREATE POLICY "Block all access to admin_keys" ON admin_keys
    FOR ALL USING (false);

-- 3. ALLOW PUBLIC TO SUBMIT SCRIPT REQUESTS BUT NOT READ OTHERS
CREATE POLICY "Allow public script submissions" ON script_requests
    FOR INSERT WITH CHECK (true);

-- Block reading script requests from public
CREATE POLICY "Block public reading of script_requests" ON script_requests
    FOR SELECT USING (false);

-- 4. <PERSON><PERSON><PERSON><PERSON> ALL ACCESS TO USERS TABLE
CREATE POLICY "Block all access to users" ON users
    FOR ALL USING (false);

-- 5. BLOCK ALL ACCESS TO BLACKLISTED HWIDS
CREATE POLICY "Block all access to blacklisted_hwids" ON blacklisted_hwids
    FOR ALL USING (false);

-- 6. VERIFY THE POLICIES ARE WORKING
-- Run this to check if RLS is enabled:
SELECT schemaname, tablename, rowsecurity 
FROM pg_tables 
WHERE tablename IN ('admin_keys', 'script_requests', 'users', 'blacklisted_hwids');

-- Should show rowsecurity = true for all tables

-- 7. TEST WITH ANON ROLE
-- These should return empty results or errors:
-- SELECT * FROM admin_keys;
-- SELECT * FROM users;
-- SELECT * FROM blacklisted_hwids;
-- SELECT * FROM script_requests;

-- 8. ONLY SCRIPT SUBMISSIONS SHOULD WORK
-- This should still work:
-- INSERT INTO script_requests (game_name, game_link, script_description, discord_username, status) 
-- VALUES ('Test', 'https://test.com', 'Test description', 'test_user', 'Pending');
