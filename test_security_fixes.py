#!/usr/bin/env python3
"""
Test script to verify security fixes are working
Run this AFTER your friend implements the fixes
"""

import requests
import json

def test_security_fixes():
    """Test if the security vulnerabilities are fixed"""
    
    # The same credentials that were exposed
    supabase_url = "https://ywecnsbsgpeqzyxxxhhm.supabase.co"
    anon_key = "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6Inl3ZWNuc2JzZ3BlcXp5eHh4aGhtIiwicm9sZSI6ImFub24iLCJpYXQiOjE3NTA4ODYzNDAsImV4cCI6MjA2NjQ2MjM0MH0.nJ8X9nos1IMvO3zsqrSP9vIuAsAVLUt48kIrtUOfocI"
    
    headers = {
        'Authorization': f'Bearer {anon_key}',
        'apikey': anon_key,
        'Content-Type': 'application/json'
    }
    
    print("🔒 TESTING SECURITY FIXES")
    print("=" * 40)
    
    # Test 1: Admin keys should be blocked
    print("\n1. Testing admin_keys access (should be BLOCKED):")
    try:
        response = requests.get(f"{supabase_url}/rest/v1/admin_keys", headers=headers)
        if response.status_code == 200:
            print("❌ STILL VULNERABLE! Admin keys are accessible")
            print(f"   Found {len(response.json())} admin keys")
        elif response.status_code == 401:
            print("✅ FIXED! Admin keys are now protected")
        else:
            print(f"⚠️ Unexpected response: {response.status_code}")
    except Exception as e:
        print(f"⚠️ Error testing admin_keys: {e}")
    
    # Test 2: Script requests should be limited
    print("\n2. Testing script_requests access:")
    try:
        response = requests.get(f"{supabase_url}/rest/v1/script_requests", headers=headers)
        if response.status_code == 200:
            data = response.json()
            if len(data) == 0:
                print("✅ GOOD! No script requests visible (or none exist)")
            else:
                print(f"⚠️ Can still see {len(data)} script requests")
                print("   This might be OK if RLS allows public read")
        elif response.status_code == 401:
            print("✅ FIXED! Script requests are now protected")
        else:
            print(f"⚠️ Unexpected response: {response.status_code}")
    except Exception as e:
        print(f"⚠️ Error testing script_requests: {e}")
    
    # Test 3: Users table should be blocked
    print("\n3. Testing users table access (should be BLOCKED):")
    try:
        response = requests.get(f"{supabase_url}/rest/v1/users", headers=headers)
        if response.status_code == 200:
            print("❌ STILL VULNERABLE! Users table is accessible")
            print(f"   Found {len(response.json())} users")
        elif response.status_code == 401:
            print("✅ FIXED! Users table is now protected")
        else:
            print(f"⚠️ Unexpected response: {response.status_code}")
    except Exception as e:
        print(f"⚠️ Error testing users: {e}")
    
    # Test 4: Script submission should still work
    print("\n4. Testing script submission (should still WORK):")
    test_payload = {
        "game_name": "Security Test Game",
        "game_link": "https://www.roblox.com/games/123456789/Test-Game",
        "script_description": "Testing if submissions still work after security fixes",
        "discord_username": "security_tester"
    }
    
    try:
        response = requests.post(
            f"{supabase_url}/rest/v1/script_requests",
            json=test_payload,
            headers=headers
        )
        if response.status_code in [200, 201]:
            print("✅ GOOD! Script submissions still work")
        elif response.status_code == 401:
            print("❌ BROKEN! Script submissions are blocked (this should work)")
        else:
            print(f"⚠️ Unexpected response: {response.status_code}")
            print(f"   Response: {response.text}")
    except Exception as e:
        print(f"⚠️ Error testing submission: {e}")
    
    print("\n" + "=" * 40)
    print("🎯 SECURITY TEST COMPLETE")
    print("\nIf you see ✅ FIXED messages, the security is improved!")
    print("If you see ❌ STILL VULNERABLE, more work needed!")

if __name__ == "__main__":
    test_security_fixes()
